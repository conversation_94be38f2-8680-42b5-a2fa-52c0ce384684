# 证书生成器 (Certificate Maker)

一个基于 Next.js + TailwindCSS + Shadcn/ui 的在线证书生成工具，让用户能够轻松创建专业的PDF证书。

## 🎯 项目概述

### 核心功能
- 🎨 **多样化模板**：提供4种不同风格的证书模板
- ✏️ **简单易用**：直观的表单界面，无需设计技能
- 👀 **实时预览**：输入内容时实时查看证书效果
- 📄 **高质量PDF**：一键生成高分辨率PDF证书
- 📱 **响应式设计**：完美适配桌面端和移动端
- 🆓 **完全免费**：无需注册，即用即走

### 技术特色
- ⚡ **高性能**：基于Next.js 14，优化的加载速度
- 🎨 **现代UI**：使用Shadcn/ui组件库，界面美观
- 🔧 **类型安全**：完整的TypeScript支持
- 📦 **轻量级**：纯前端实现，无服务器依赖

## 🏗️ 技术架构

### 技术栈
- **前端框架**：Next.js 14 (App Router)
- **样式框架**：TailwindCSS
- **UI组件**：Shadcn/ui
- **PDF生成**：PDF-lib
- **表单管理**：React Hook Form + Zod
- **类型检查**：TypeScript
- **部署平台**：Vercel

### 项目结构
```
certificate-maker/
├── app/                    # Next.js App Router
├── components/             # React组件
│   ├── ui/                # Shadcn/ui基础组件
│   ├── certificate/       # 证书相关组件
│   └── layout/            # 布局组件
├── lib/                   # 工具库和业务逻辑
├── types/                 # TypeScript类型定义
├── public/                # 静态资源
└── docs/                  # 项目文档
```

## 📋 开发计划

### 第一阶段：项目基础搭建 (3-4天)
- [x] 项目规划与文档编写
- [ ] 技术架构设计
- [ ] 项目初始化
- [ ] 基础布局和路由
- [ ] 设计系统建立
- [ ] 证书模板设计

### 第二阶段：核心功能开发 (5-6天)
- [ ] 表单组件开发
- [ ] 证书预览系统
- [ ] PDF生成功能

### 第三阶段：用户体验优化 (3-4天)
- [ ] 响应式设计完善
- [ ] 性能优化
- [ ] 用户体验细节

### 第四阶段：测试与部署 (2-3天)
- [ ] 功能测试
- [ ] 部署配置
- [ ] 监控和分析

## 🎨 证书模板

### 模板类型
1. **经典商务风格**
   - 蓝色边框设计
   - 正式商务场合适用
   - 适合企业培训证书

2. **现代简约风格**
   - 黑白配色方案
   - 简洁现代设计
   - 适合技术认证证书

3. **优雅绿色风格**
   - 绿色装饰元素
   - 自然优雅风格
   - 适合环保主题证书

4. **传统正式风格**
   - 棕色经典边框
   - 传统正式设计
   - 适合学术成就证书

## 🚀 快速开始

### 环境要求
- Node.js 18.0+
- npm 或 yarn 或 pnpm

### 安装依赖
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 开发环境
```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

### 构建生产版本
```bash
npm run build
# 或
yarn build
# 或
pnpm build
```

## 📖 使用指南

### 基本使用流程
1. **选择模板**：从4个预设模板中选择喜欢的样式
2. **填写信息**：
   - 姓名：获奖者或证书持有人姓名
   - 日期：证书颁发日期
   - 签名：颁发者签名或机构名称
   - 详情：证书的详细描述或获奖原因
3. **预览效果**：实时查看证书生成效果
4. **生成下载**：点击"生成PDF"按钮下载证书

### 最佳实践
- **姓名**：建议使用全名，避免过长
- **日期**：使用标准日期格式（如：2024年1月1日）
- **签名**：可以是个人姓名或机构名称
- **详情**：详细描述获奖原因或证书用途，建议50-200字

## 🔧 开发指南

### 代码规范
- 使用 ESLint + Prettier 进行代码格式化
- 遵循 TypeScript 严格模式
- 组件使用函数式组件 + Hooks
- 样式使用 TailwindCSS 原子类

### 组件开发
```typescript
// 示例组件结构
interface ComponentProps {
  // 定义组件属性类型
}

export function Component({ ...props }: ComponentProps) {
  // 组件逻辑
  return (
    <div className="tailwind-classes">
      {/* 组件内容 */}
    </div>
  );
}
```

### 测试
```bash
# 运行测试
npm run test

# 测试覆盖率
npm run test:coverage

# E2E测试
npm run test:e2e
```

## 📊 性能指标

### 目标指标
- **首屏加载时间**：< 2秒
- **PDF生成时间**：< 5秒
- **Lighthouse评分**：> 90分
- **移动端适配**：完美支持

### 优化策略
- 代码分割和懒加载
- 图片和字体优化
- 缓存策略
- CDN加速

## 🤝 贡献指南

### 开发流程
1. Fork 项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React框架
- [TailwindCSS](https://tailwindcss.com/) - CSS框架
- [Shadcn/ui](https://ui.shadcn.com/) - UI组件库
- [PDF-lib](https://pdf-lib.js.org/) - PDF生成库
- [Bannerbear](https://www.bannerbear.com/) - 设计灵感来源

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 📧 邮箱：[<EMAIL>]
- 🐛 问题反馈：[GitHub Issues]
- 💬 讨论交流：[GitHub Discussions]

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！
