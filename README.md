# Certificate Maker

An online certificate generation tool built with Next.js + TailwindCSS + Shadcn/ui, designed specifically for English-speaking users to easily create professional PDF certificates.

## 🎯 Project Overview

### Core Features
- 🎨 **4 Fixed Templates**: Pre-configured certificate templates with fixed layouts
- ✏️ **Simple & Intuitive**: User-friendly form interface with character limits
- 👀 **Real-time Preview**: Instant certificate preview as you type
- 📄 **High-Quality PDF**: One-click generation of high-resolution PDF certificates
- 📱 **Mobile-First Design**: Optimized primarily for mobile devices
- 🆓 **Completely Free**: No registration required, instant use
- 🌍 **English-Only**: Designed specifically for English-speaking markets

### Technical Highlights
- ⚡ **High Performance**: Built on Next.js 14 with optimized loading speeds
- 🎨 **Modern UI**: Beautiful interface using Shadcn/ui component library
- 🔧 **Type Safety**: Complete TypeScript support
- 📦 **Lightweight**: Pure frontend implementation, no server dependencies
- 📱 **Mobile-Optimized**: Touch-friendly interactions and responsive design

## 🏗️ 技术架构

### 技术栈
- **前端框架**：Next.js 14 (App Router)
- **样式框架**：TailwindCSS
- **UI组件**：Shadcn/ui
- **PDF生成**：PDF-lib
- **表单管理**：React Hook Form + Zod
- **类型检查**：TypeScript
- **部署平台**：Vercel

### 项目结构
```
certificate-maker/
├── app/                    # Next.js App Router
├── components/             # React组件
│   ├── ui/                # Shadcn/ui基础组件
│   ├── certificate/       # 证书相关组件
│   └── layout/            # 布局组件
├── lib/                   # 工具库和业务逻辑
├── types/                 # TypeScript类型定义
├── public/                # 静态资源
└── docs/                  # 项目文档
```

## 📋 开发计划

### 第一阶段：项目基础搭建 (3-4天)
- [x] 项目规划与文档编写
- [ ] 技术架构设计
- [ ] 项目初始化
- [ ] 基础布局和路由
- [ ] 设计系统建立
- [ ] 证书模板设计

### 第二阶段：核心功能开发 (5-6天)
- [ ] 表单组件开发
- [ ] 证书预览系统
- [ ] PDF生成功能

### 第三阶段：用户体验优化 (3-4天)
- [ ] 响应式设计完善
- [ ] 性能优化
- [ ] 用户体验细节

### 第四阶段：测试与部署 (2-3天)
- [ ] 功能测试
- [ ] 部署配置
- [ ] 监控和分析

## 🎨 Certificate Templates

### Template Types (Fixed Configuration)
1. **Classic Business Style**
   - Blue border design with fixed positioning
   - Formal business occasions
   - Perfect for corporate training certificates
   - **Character Limits**: Name (50), Date (20), Signature (30), Details (200)

2. **Modern Minimalist Style**
   - Black & white color scheme
   - Clean modern design
   - Ideal for technical certifications
   - **Character Limits**: Name (50), Date (20), Signature (30), Details (200)

3. **Elegant Green Style**
   - Green decorative elements
   - Natural elegant style
   - Great for environmental theme certificates
   - **Character Limits**: Name (50), Date (20), Signature (30), Details (200)

4. **Traditional Formal Style**
   - Brown classic border
   - Traditional formal design
   - Perfect for academic achievement certificates
   - **Character Limits**: Name (50), Date (20), Signature (30), Details (200)

### Template Features
- **Fixed Layout**: Pre-configured text positions and sizes
- **Character Constraints**: Strict limits to ensure proper formatting
- **Mobile-Optimized**: Templates designed for mobile viewing
- **High-Quality**: Vector-based designs for crisp PDF output

## 🚀 快速开始

### 环境要求
- Node.js 18.0+
- npm 或 yarn 或 pnpm

### 安装依赖
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 开发环境
```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

### 构建生产版本
```bash
npm run build
# 或
yarn build
# 或
pnpm build
```

## 📖 User Guide

### Basic Usage Flow
1. **Select Template**: Choose from 4 pre-configured template styles
2. **Fill Information** (within character limits):
   - **Name**: Recipient or certificate holder name (max 50 characters)
   - **Date**: Certificate issue date (max 20 characters)
   - **Signature**: Issuer signature or organization name (max 30 characters)
   - **Details**: Certificate description or achievement reason (max 200 characters)
3. **Preview Effect**: Real-time certificate generation preview
4. **Generate & Download**: Click "Generate PDF" button to download certificate

### Best Practices
- **Name**: Use full name, keep within 50 character limit
- **Date**: Use standard date format (e.g., January 1, 2024)
- **Signature**: Can be personal name or organization name (max 30 chars)
- **Details**: Detailed description of achievement or certificate purpose (50-200 chars recommended)

### Mobile Usage Tips
- **Touch Targets**: All buttons and inputs are optimized for touch
- **Character Counters**: Real-time feedback on remaining characters
- **Portrait Mode**: Best experience in portrait orientation
- **Zoom Preview**: Pinch to zoom on certificate preview

## 🔧 开发指南

### 代码规范
- 使用 ESLint + Prettier 进行代码格式化
- 遵循 TypeScript 严格模式
- 组件使用函数式组件 + Hooks
- 样式使用 TailwindCSS 原子类

### 组件开发
```typescript
// 示例组件结构
interface ComponentProps {
  // 定义组件属性类型
}

export function Component({ ...props }: ComponentProps) {
  // 组件逻辑
  return (
    <div className="tailwind-classes">
      {/* 组件内容 */}
    </div>
  );
}
```

### 测试
```bash
# 运行测试
npm run test

# 测试覆盖率
npm run test:coverage

# E2E测试
npm run test:e2e
```

## 📊 性能指标

### 目标指标
- **首屏加载时间**：< 2秒
- **PDF生成时间**：< 5秒
- **Lighthouse评分**：> 90分
- **移动端适配**：完美支持

### 优化策略
- 代码分割和懒加载
- 图片和字体优化
- 缓存策略
- CDN加速

## 🤝 贡献指南

### 开发流程
1. Fork 项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React框架
- [TailwindCSS](https://tailwindcss.com/) - CSS框架
- [Shadcn/ui](https://ui.shadcn.com/) - UI组件库
- [PDF-lib](https://pdf-lib.js.org/) - PDF生成库
- [Bannerbear](https://www.bannerbear.com/) - 设计灵感来源

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 📧 邮箱：[<EMAIL>]
- 🐛 问题反馈：[GitHub Issues]
- 💬 讨论交流：[GitHub Discussions]

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！
