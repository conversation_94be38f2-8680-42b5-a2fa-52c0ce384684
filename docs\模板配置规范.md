# Certificate Template Configuration Specifications

## Overview
This document defines the exact specifications for each certificate template, including fixed positioning, character limits, fonts, and colors. All templates are designed with mobile-first approach and English-only content.

## Template Dimensions
- **Standard Size**: A4 (595.28 × 841.89 points)
- **DPI**: 300 for high-quality printing
- **Orientation**: Portrait
- **Margins**: 50pt on all sides

## Template 1: Classic Business Style

### Visual Design
- **Primary Color**: #1E40AF (Blue)
- **Secondary Color**: #3B82F6 (Light Blue)
- **Background**: #FFFFFF (White)
- **Border**: 3pt solid blue border with corner decorations

### Typography
```typescript
const classicBusinessFonts = {
  title: {
    family: 'Playfair Display',
    size: 36,
    weight: 700,
    color: '#1E40AF'
  },
  name: {
    family: 'Playfair Display',
    size: 28,
    weight: 600,
    color: '#1F2937'
  },
  body: {
    family: 'Inter',
    size: 14,
    weight: 400,
    color: '#374151'
  },
  signature: {
    family: 'Dancing Script',
    size: 24,
    weight: 400,
    color: '#1F2937'
  }
};
```

### Field Positions (Fixed Coordinates)
```typescript
const classicBusinessLayout = {
  title: {
    text: "CERTIFICATE OF ACHIEVEMENT",
    x: 297.64, // Center
    y: 150,
    width: 400,
    height: 50,
    align: 'center'
  },
  name: {
    x: 297.64, // Center
    y: 300,
    width: 400,
    height: 40,
    align: 'center',
    maxLength: 50
  },
  details: {
    x: 297.64, // Center
    y: 400,
    width: 450,
    height: 100,
    align: 'center',
    maxLength: 200
  },
  date: {
    x: 150,
    y: 650,
    width: 150,
    height: 30,
    align: 'left',
    maxLength: 20
  },
  signature: {
    x: 445,
    y: 650,
    width: 150,
    height: 30,
    align: 'right',
    maxLength: 30
  }
};
```

## Template 2: Modern Minimalist Style

### Visual Design
- **Primary Color**: #000000 (Black)
- **Secondary Color**: #6B7280 (Gray)
- **Background**: #FFFFFF (White)
- **Border**: Minimal thin line accents

### Typography
```typescript
const modernMinimalistFonts = {
  title: {
    family: 'Inter',
    size: 32,
    weight: 800,
    color: '#000000'
  },
  name: {
    family: 'Inter',
    size: 26,
    weight: 600,
    color: '#000000'
  },
  body: {
    family: 'Inter',
    size: 14,
    weight: 400,
    color: '#374151'
  },
  signature: {
    family: 'Inter',
    size: 16,
    weight: 500,
    color: '#6B7280'
  }
};
```

### Field Positions (Fixed Coordinates)
```typescript
const modernMinimalistLayout = {
  title: {
    text: "CERTIFICATE",
    x: 297.64,
    y: 180,
    width: 300,
    height: 40,
    align: 'center'
  },
  name: {
    x: 297.64,
    y: 320,
    width: 400,
    height: 35,
    align: 'center',
    maxLength: 50
  },
  details: {
    x: 297.64,
    y: 420,
    width: 450,
    height: 80,
    align: 'center',
    maxLength: 200
  },
  date: {
    x: 150,
    y: 600,
    width: 150,
    height: 25,
    align: 'left',
    maxLength: 20
  },
  signature: {
    x: 445,
    y: 600,
    width: 150,
    height: 25,
    align: 'right',
    maxLength: 30
  }
};
```

## Template 3: Elegant Green Style

### Visual Design
- **Primary Color**: #059669 (Green)
- **Secondary Color**: #10B981 (Light Green)
- **Background**: #F0FDF4 (Very Light Green)
- **Border**: Decorative green border with leaf motifs

### Typography
```typescript
const elegantGreenFonts = {
  title: {
    family: 'Crimson Text',
    size: 34,
    weight: 600,
    color: '#059669'
  },
  name: {
    family: 'Crimson Text',
    size: 26,
    weight: 600,
    color: '#1F2937'
  },
  body: {
    family: 'Source Sans Pro',
    size: 14,
    weight: 400,
    color: '#374151'
  },
  signature: {
    family: 'Great Vibes',
    size: 22,
    weight: 400,
    color: '#059669'
  }
};
```

## Template 4: Traditional Formal Style

### Visual Design
- **Primary Color**: #92400E (Brown)
- **Secondary Color**: #D97706 (Light Brown)
- **Background**: #FFFBEB (Cream)
- **Border**: Classic formal border with traditional ornaments

### Typography
```typescript
const traditionalFormalFonts = {
  title: {
    family: 'Libre Baskerville',
    size: 32,
    weight: 700,
    color: '#92400E'
  },
  name: {
    family: 'Libre Baskerville',
    size: 24,
    weight: 600,
    color: '#1F2937'
  },
  body: {
    family: 'Libre Baskerville',
    size: 13,
    weight: 400,
    color: '#374151'
  },
  signature: {
    family: 'Allura',
    size: 26,
    weight: 400,
    color: '#92400E'
  }
};
```

## Character Limits (Universal)

### Field Constraints
```typescript
const universalConstraints = {
  name: {
    maxLength: 50,
    minLength: 1,
    validation: /^[a-zA-Z\s\-\.\']+$/,
    errorMessage: "Name can only contain letters, spaces, hyphens, periods, and apostrophes"
  },
  date: {
    maxLength: 20,
    minLength: 1,
    validation: /^[a-zA-Z0-9\s\,\-\.]+$/,
    errorMessage: "Please enter a valid date"
  },
  signature: {
    maxLength: 30,
    minLength: 1,
    validation: /^[a-zA-Z\s\-\.\']+$/,
    errorMessage: "Signature can only contain letters, spaces, hyphens, periods, and apostrophes"
  },
  details: {
    maxLength: 200,
    minLength: 10,
    validation: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    errorMessage: "Details must be between 10-200 characters and contain only standard characters"
  }
};
```

## Mobile Optimization

### Touch Targets
- **Minimum Size**: 44px × 44px for all interactive elements
- **Spacing**: 8px minimum between touch targets
- **Input Fields**: Full-width on mobile with 16px font size to prevent zoom

### Responsive Breakpoints
```typescript
const breakpoints = {
  mobile: '320px - 767px',
  tablet: '768px - 1023px',
  desktop: '1024px+'
};

const mobileOptimizations = {
  templatePreview: {
    width: '100%',
    maxWidth: '350px',
    aspectRatio: '3:4'
  },
  formInputs: {
    fontSize: '16px', // Prevents iOS zoom
    padding: '12px',
    borderRadius: '8px'
  },
  characterCounter: {
    position: 'bottom-right',
    fontSize: '12px',
    color: '#6B7280'
  }
};
```

## Implementation Guidelines

### Template Loading
1. Templates are loaded as static configurations
2. No dynamic font size adjustment
3. Text overflow is handled by truncation with ellipsis
4. Character limits are enforced at input level

### PDF Generation
1. Use exact coordinates for text placement
2. Embed fonts for consistent rendering
3. Generate at 300 DPI for print quality
4. Maintain aspect ratio across devices

### Error Handling
1. Character limit exceeded: Show warning and prevent input
2. Invalid characters: Filter out automatically
3. Empty required fields: Show validation error
4. PDF generation failure: Provide retry option

This specification ensures consistent, high-quality certificate generation across all devices while maintaining the fixed template approach requested.
